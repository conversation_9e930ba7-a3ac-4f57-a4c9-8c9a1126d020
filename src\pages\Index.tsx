import { But<PERSON> } from '@/components/ui/button';
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, Star, Book } from 'lucide-react';
import { Hero } from '@/components/layout/Hero';
import PageLayout from '@/components/layout/PageLayout';

import { useNavigation } from '@/hooks/useNavigation';


const Index = () => {
  const { handleGetStarted } = useNavigation();

  console.log('🏠 Index render: Home page');

  return (
    <PageLayout>
      <Hero onGetStarted={handleGetStarted} />

      {/* Features Section */}
      <section className="py-20 px-4" aria-labelledby="features-heading">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-16">
            <h2 id="features-heading" className="text-3xl font-bold text-gradient mb-4">
              Nurture Your Inner Growth
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              AmberGlow provides a safe, mindful space for daily reflection and emotional processing
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <Card className="glass-effect hover:warm-glow transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-amber-200 transition-colors">
                  <Book className="w-8 h-8 text-amber-600" />
                </div>
                <CardTitle>Daily Journal</CardTitle>
                <CardDescription>
                  Express your thoughts and feelings in a beautiful, distraction-free writing
                  environment
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="glass-effect hover:warm-glow transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-orange-200 transition-colors">
                  <Star className="w-8 h-8 text-orange-600" />
                </div>
                <CardTitle>Emotion Tracking</CardTitle>
                <CardDescription>
                  Track your emotional patterns and discover insights about your mental wellbeing
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="glass-effect hover:warm-glow transition-all duration-300 group">
              <CardHeader className="text-center">
                <div className="w-16 h-16 bg-warm-100 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:bg-warm-200 transition-colors">
                  <Calendar className="w-8 h-8 text-amber-700" />
                </div>
                <CardTitle>Growth Analytics</CardTitle>
                <CardDescription>
                  Visualize your personal growth journey with mindful insights and progress tracking
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-amber-50 to-orange-50">
        <div className="container mx-auto text-center max-w-2xl">
          <h2 className="text-3xl font-bold text-gradient mb-6">
            Begin Your Mindful Journey Today
          </h2>
          <p className="text-lg text-muted-foreground mb-8">
            Take the first step towards deeper self-understanding and emotional wellness
          </p>
          <Button
            size="lg"
            onClick={handleGetStarted}
            className="bg-amber-500 hover:bg-amber-600 text-white px-8 py-3 text-lg animate-gentle-bounce"
          >
            Start Writing
          </Button>
        </div>
      </section>
    </PageLayout>
  );
};

export default Index;

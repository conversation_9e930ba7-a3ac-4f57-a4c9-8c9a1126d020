/* Import accessibility styles */
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Amberglow Design System - All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 32 30% 98%;
    --foreground: 30 20% 15%;

    --card: 32 30% 98%;
    --card-foreground: 30 20% 15%;

    --popover: 32 30% 98%;
    --popover-foreground: 30 20% 15%;

    --primary: 29 95% 57%; /* Amber #FFA726 */
    --primary-foreground: 0 0% 100%;

    --secondary: 28 100% 88%; /* Light amber */
    --secondary-foreground: 30 40% 20%;

    --muted: 28 50% 95%;
    --muted-foreground: 30 20% 45%;

    --accent: 28 100% 88%;
    --accent-foreground: 30 40% 20%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 28 30% 90%;
    --input: 28 30% 90%;
    --ring: 29 95% 57%;

    --radius: 0.75rem;

    --sidebar-background: 32 30% 98%;
    --sidebar-foreground: 30 20% 45%;
    --sidebar-primary: 29 95% 57%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 28 50% 95%;
    --sidebar-accent-foreground: 30 40% 20%;
    --sidebar-border: 28 30% 90%;
    --sidebar-ring: 29 95% 57%;
  }

  .dark {
    --background: 30 15% 8%;
    --foreground: 28 100% 92%;

    --card: 30 15% 10%;
    --card-foreground: 28 100% 92%;

    --popover: 30 15% 10%;
    --popover-foreground: 28 100% 92%;

    --primary: 29 85% 65%; /* Warmer amber for dark mode */
    --primary-foreground: 30 20% 15%;

    --secondary: 30 20% 15%;
    --secondary-foreground: 28 80% 85%;

    --muted: 30 15% 15%;
    --muted-foreground: 28 50% 60%;

    --accent: 30 20% 15%;
    --accent-foreground: 28 80% 85%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 15% 20%;
    --input: 30 15% 20%;
    --ring: 29 85% 65%;

    --sidebar-background: 30 15% 8%;
    --sidebar-foreground: 28 50% 60%;
    --sidebar-primary: 29 85% 65%;
    --sidebar-primary-foreground: 30 20% 15%;
    --sidebar-accent: 30 15% 15%;
    --sidebar-accent-foreground: 28 100% 92%;
    --sidebar-border: 30 15% 20%;
    --sidebar-ring: 29 85% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-inter;
    background-image:
      radial-gradient(circle at 20% 80%, rgba(255, 167, 38, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 112, 67, 0.1) 0%, transparent 50%);
    min-height: 100vh;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-lora;
  }
}

@layer components {
  .glass-effect {
    @apply backdrop-blur-sm bg-white/20 border border-white/30;
  }

  .warm-glow {
    box-shadow: 0 0 20px rgba(255, 167, 38, 0.3);
  }

  .text-gradient {
    background: linear-gradient(135deg, #ffa726, #ff7043);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-warm-50;
}

::-webkit-scrollbar-thumb {
  @apply bg-amber-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-amber-400;
}

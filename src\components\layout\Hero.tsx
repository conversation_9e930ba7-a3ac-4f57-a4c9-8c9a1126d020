import { Button } from '@/components/ui/button';
import { ArrowDown } from 'lucide-react';

interface HeroProps {
  onGetStarted: () => void;
}

export const Hero = ({ onGetStarted }: HeroProps) => {
  return (
    <section className="relative min-h-screen flex items-center justify-center px-4 overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-32 h-32 bg-amber-200 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-32 right-10 w-48 h-48 bg-orange-200 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-warm-100 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>

      <div className="relative z-10 text-center max-w-4xl mx-auto">
        <div className="animate-fade-in">
          <h1 className="text-5xl md:text-7xl font-bold text-gradient mb-6 leading-tight">
            Amberglow
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 font-lora max-w-2xl mx-auto leading-relaxed">
            A mindful space for daily reflection, emotional processing, and personal growth through
            journaling
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              onClick={onGetStarted}
              className="bg-amber-500 hover:bg-amber-600 text-white px-8 py-4 text-lg font-medium hover:scale-105 transition-all duration-200 warm-glow"
            >
              Begin Your Journey
            </Button>
            <Button
              variant="outline"
              size="lg"
              className="border-amber-300 text-amber-700 hover:bg-amber-50 px-8 py-4 text-lg font-medium hover:scale-105 transition-all duration-200"
            >
              Learn More
            </Button>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-gentle-bounce">
          <ArrowDown className="w-6 h-6 text-amber-500" />
        </div>
      </div>

      {/* Floating elements */}
      <div className="absolute top-1/4 left-8 w-2 h-2 bg-amber-400 rounded-full animate-pulse"></div>
      <div className="absolute top-1/3 right-12 w-3 h-3 bg-orange-400 rounded-full animate-pulse delay-500"></div>
      <div className="absolute bottom-1/4 left-16 w-1 h-1 bg-warm-300 rounded-full animate-pulse delay-1000"></div>
      <div className="absolute bottom-1/3 right-8 w-2 h-2 bg-amber-300 rounded-full animate-pulse delay-1500"></div>
    </section>
  );
};
